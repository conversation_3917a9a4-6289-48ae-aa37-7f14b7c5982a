'use client';

import { Ch<PERSON><PERSON>Down, <PERSON><PERSON><PERSON>, User, CreditCard, Users, LogOut } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuGroup,
   DropdownMenuItem,
   DropdownMenuLabel,
   DropdownMenuSeparator,
   DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';

// This would normally come from a global state or context
const userData = {
   name: 'shadcn',
   email: '<EMAIL>',
   avatar: '/avatars/shadcn.jpg',
};

export function UserProfileDropdown() {
   const signOut = () => {
      // Clear local storage
      localStorage.clear();
      // Redirect to login page
      window.location.href = '/';
   };

   return (
      <DropdownMenu>
         <DropdownMenuTrigger asChild>
            <Button variant="outline" className="flex items-center gap-2 px-3 py-2 h-10">
               <Avatar className='h-8 w-8'>
                  <AvatarImage src={userData.avatar} alt={userData.name} />
                  <AvatarFallback className="bg-gray-200">
                     {userData.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </AvatarFallback>
               </Avatar>
               <div className='hidden md:flex flex-col items-start'>
                  <span className='text-sm font-medium text-gray-900'>{userData.name}</span>
                  <span className='text-xs text-gray-500'>{userData.email}</span>
               </div>
               <ChevronDown className='h-4 w-4 text-gray-500' />
            </Button>
         </DropdownMenuTrigger>
         
         <DropdownMenuContent
            className='w-64 rounded-lg'
            align='end'
            sideOffset={8}
         >
            <DropdownMenuLabel className='p-0 font-normal'>
               <div className='flex items-center gap-2 px-3 py-2 text-left text-sm'>
                  <Avatar className='h-8 w-8'>
                     <AvatarImage src={userData.avatar} alt={userData.name} />
                     <AvatarFallback className="bg-gray-200">
                        {userData.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                     </AvatarFallback>
                  </Avatar>
                  <div className='grid flex-1 text-left text-sm leading-tight'>
                     <span className='truncate font-medium text-gray-900'>{userData.name}</span>
                     <span className='truncate text-xs text-gray-500'>{userData.email}</span>
                  </div>
               </div>
            </DropdownMenuLabel>
            
            <DropdownMenuSeparator />
            
            <DropdownMenuGroup>
               <DropdownMenuItem className="cursor-pointer">
                  <User className="h-4 w-4" />
                  <span>Profile</span>
                  <span className="ml-auto text-xs text-gray-500">⌘P</span>
               </DropdownMenuItem>
               
               <DropdownMenuItem className="cursor-pointer">
                  <CreditCard className="h-4 w-4" />
                  <span>Billing</span>
                  <span className="ml-auto text-xs text-gray-500">⌘B</span>
               </DropdownMenuItem>
               
               <DropdownMenuItem className="cursor-pointer">
                  <Settings className="h-4 w-4" />
                  <span>Settings</span>
                  <span className="ml-auto text-xs text-gray-500">⌘S</span>
               </DropdownMenuItem>
               
               <DropdownMenuItem className="cursor-pointer">
                  <Users className="h-4 w-4" />
                  <span>New Team</span>
               </DropdownMenuItem>
            </DropdownMenuGroup>
            
            <DropdownMenuSeparator />
            
            <DropdownMenuItem onClick={signOut} className="cursor-pointer text-red-600">
               <LogOut className="h-4 w-4" />
               <span>Log out</span>
               <span className="ml-auto text-xs text-gray-500">⌘Q</span>
            </DropdownMenuItem>
         </DropdownMenuContent>
      </DropdownMenu>
   );
} 