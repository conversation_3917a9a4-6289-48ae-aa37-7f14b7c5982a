'use client';

import { ErrorMessage } from '@/components/error-message';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import {
   Sheet,
   SheetClose,
   SheetContent,
   SheetDescription,
   SheetFooter,
   SheetHeader,
   SheetTitle,
} from '@/components/ui/sheet';

import { toast } from '@/lib/toast';
import { cn } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import React, { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as z from 'zod';
import { useUpdateDriver } from '../api/mutations';
import { useGetDriver, useCities } from '../api/queries';
import { useQueryClient } from '@tanstack/react-query';

// Define the Zod schema for form validation
const editDriverFormSchema = z.object({
   firstName: z
      .string()
      .min(1, 'First name is required')
      .min(2, 'First name must be at least 2 characters'),
   lastName: z
      .string()
      .min(1, 'Last name is required')
      .min(2, 'Last name must be at least 2 characters'),
   gender: z.enum(['MALE', 'FEMALE', 'OTHER'], {
      message: 'Gender is required',
   }),
   dob: z.date({
      message: 'Date of birth is required',
   }),
   email: z.string().min(1, 'Email is required').email('Please enter a valid email address'),
   cityId: z.string().min(1, 'City is required'),
   referralCode: z.string().optional(),
});

// Infer the type from the schema
type EditDriverFormValues = z.infer<typeof editDriverFormSchema>;

interface EditDriverProps {
   driverId: string | null;
   isOpen: boolean;
   onClose: () => void;
}

export const EditDriver = ({ driverId, isOpen, onClose }: EditDriverProps) => {
   const updateDriverMutation = useUpdateDriver();
   const queryClient = useQueryClient();
   const { data: driverResponse, isLoading } = useGetDriver(driverId);
   const driver = driverResponse?.data;
   const citiesQuery = useCities();

   const form = useForm<EditDriverFormValues>({
      resolver: zodResolver(editDriverFormSchema),
      defaultValues: {
         firstName: '',
         lastName: '',
         gender: 'MALE',
         dob: new Date(),
         email: '',
         cityId: '',
         referralCode: '',
      },
   });

   const {
      formState: { errors },
      reset,
      control,
   } = form;

   // Reset form when driver data is loaded
   useEffect(() => {
      if (driver) {
         reset({
            firstName: driver.firstName || '',
            lastName: driver.lastName || '',
            gender: driver.gender || 'MALE',
            dob: driver.dob ? new Date(driver.dob) : new Date(),
            email: driver.email || '',
            cityId: driver.cityId || '',
            referralCode: driver.referralCode || '',
         });
      }
   }, [driver, reset]);

   const onSubmit = async (data: EditDriverFormValues) => {
      if (!driverId) return;

      updateDriverMutation.mutate(
         {
            id: driverId,
            firstName: data.firstName,
            lastName: data.lastName,
            gender: data.gender,
            dob: data.dob.toISOString(),
            email: data.email,
            cityId: data.cityId,
            referralCode: data.referralCode,
         },
         {
            onSuccess: () => {
               toast.success('Driver updated successfully');
               onClose();
               queryClient.invalidateQueries({ queryKey: ['drivers'] });
            },
            onError: (error: any) => {
               toast.error(error?.message || 'Failed to update driver');
            },
         }
      );
   };

   return (
      <Sheet open={isOpen} onOpenChange={onClose}>
         <SheetContent
            style={{
               maxWidth: '475px',
            }}
         >
            <form className='flex h-full w-full flex-col' onSubmit={form.handleSubmit(onSubmit)}>
               <SheetHeader className='flex-none'>
                  <SheetTitle>Edit Driver</SheetTitle>
                  <SheetDescription>Update driver information</SheetDescription>
               </SheetHeader>

               <div className='flex-1 overflow-y-auto' id='form-container'>
                  {isLoading ? (
                     <div className='flex items-center justify-center py-8'>
                        <div className='text-gray-500'>Loading driver data...</div>
                     </div>
                  ) : (
                     <div className='space-y-4 px-4 py-4'>
                        <div className='grid grid-cols-2 gap-4'>
                           <div className='flex flex-col gap-2'>
                              <Label htmlFor='firstName'>First Name *</Label>
                              <Controller
                                 control={control}
                                 name='firstName'
                                 render={({ field }) => (
                                    <Input
                                       id='firstName'
                                       placeholder='Enter first name'
                                       {...field}
                                       className='w-full'
                                    />
                                 )}
                              />
                              {errors.firstName && <ErrorMessage error={errors.firstName} />}
                           </div>

                           <div className='flex flex-col gap-2'>
                              <Label htmlFor='lastName'>Last Name *</Label>
                              <Controller
                                 control={control}
                                 name='lastName'
                                 render={({ field }) => (
                                    <Input
                                       id='lastName'
                                       placeholder='Enter last name'
                                       {...field}
                                       className='w-full'
                                    />
                                 )}
                              />
                              {errors.lastName && <ErrorMessage error={errors.lastName} />}
                           </div>
                        </div>

                        <div className='grid grid-cols-2 gap-4'>
                           <div className='flex flex-col gap-2'>
                              <Label htmlFor='gender'>Gender *</Label>
                              <Controller
                                 control={control}
                                 name='gender'
                                 render={({ field }) => (
                                    <Select onValueChange={field.onChange} value={field.value}>
                                       <SelectTrigger>
                                          <SelectValue placeholder='Select gender' />
                                       </SelectTrigger>
                                       <SelectContent>
                                          <SelectItem value='male'>Male</SelectItem>
                                          <SelectItem value='female'>Female</SelectItem>
                                          <SelectItem value='other'>Other</SelectItem>
                                       </SelectContent>
                                    </Select>
                                 )}
                              />
                              {errors.gender && <ErrorMessage error={errors.gender} />}
                           </div>

                           <div className='flex flex-col gap-2'>
                              <Label htmlFor='dob'>Date of Birth *</Label>
                              <Controller
                                 control={control}
                                 name='dob'
                                 render={({ field }) => (
                                    <Popover modal={true}>
                                       <PopoverTrigger asChild>
                                          <Button
                                             variant={'outline'}
                                             className={cn(
                                                'w-full pl-3 text-left font-normal',
                                                !field.value && 'text-muted-foreground'
                                             )}
                                          >
                                             {field.value ? (
                                                format(field.value, 'dd/MM/yyyy')
                                             ) : (
                                                <span>dd/mm/yyyy</span>
                                             )}
                                             <CalendarIcon className='ml-auto h-4 w-4 opacity-50' />
                                          </Button>
                                       </PopoverTrigger>
                                       <PopoverContent
                                          className='w-auto p-0 z-[9999]'
                                          align='start'
                                       >
                                          <Calendar
                                             mode='single'
                                             selected={field.value}
                                             onSelect={field.onChange}
                                          />
                                       </PopoverContent>
                                    </Popover>
                                 )}
                              />
                              {errors.dob && <ErrorMessage error={errors.dob} />}
                           </div>
                        </div>

                        {/* Phone number is not editable - it's verified during registration */}
                        <div className='flex flex-col gap-2'>
                           <Label>Phone Number</Label>
                           <Input
                              value={driver?.phoneNumber || ''}
                              disabled
                              className='w-full bg-gray-50'
                           />
                           <p className='text-xs text-muted-foreground'>
                              Phone number cannot be changed after verification
                           </p>
                        </div>

                        <div className='flex flex-col gap-2'>
                           <Label htmlFor='email'>Email Address *</Label>
                           <Controller
                              control={control}
                              name='email'
                              render={({ field }) => (
                                 <Input
                                    id='email'
                                    type='email'
                                    placeholder='<EMAIL>'
                                    {...field}
                                    className='w-full'
                                 />
                              )}
                           />
                           {errors.email && <ErrorMessage error={errors.email} />}
                        </div>

                        <div className='flex flex-col gap-2'>
                           <Label htmlFor='cityId'>City *</Label>
                           <Controller
                              control={control}
                              name='cityId'
                              render={({ field }) => (
                                 <Select onValueChange={field.onChange} value={field.value}>
                                    <SelectTrigger>
                                       <SelectValue placeholder='Select city' />
                                    </SelectTrigger>
                                    <SelectContent>
                                       {citiesQuery.data?.data?.map(city => (
                                          <SelectItem key={city.id} value={city.id}>
                                             {city.name}
                                          </SelectItem>
                                       ))}
                                    </SelectContent>
                                 </Select>
                              )}
                           />
                           {errors.cityId && <ErrorMessage error={errors.cityId} />}
                        </div>

                        <div className='flex flex-col gap-2'>
                           <Label htmlFor='referralCode'>Referral Code</Label>
                           <Controller
                              control={control}
                              name='referralCode'
                              render={({ field }) => (
                                 <Input
                                    id='referralCode'
                                    placeholder='Enter referral code (optional)'
                                    {...field}
                                    className='w-full'
                                 />
                              )}
                           />
                           {errors.referralCode && <ErrorMessage error={errors.referralCode} />}
                        </div>
                     </div>
                  )}
               </div>

               <SheetFooter className='flex-none p-4 grid grid-cols-2 gap-4 w-full border-t bg-white'>
                  <SheetClose asChild>
                     <Button
                        type='button'
                        variant='outline'
                        disabled={updateDriverMutation.isPending}
                     >
                        Cancel
                     </Button>
                  </SheetClose>
                  <Button type='submit' disabled={updateDriverMutation.isPending || isLoading}>
                     {updateDriverMutation.isPending ? 'Updating...' : 'Update Driver'}
                  </Button>
               </SheetFooter>
            </form>
         </SheetContent>
      </Sheet>
   );
};
