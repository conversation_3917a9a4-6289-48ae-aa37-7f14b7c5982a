'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Spinner } from '@/components/ui/spinner';
import { ErrorMessage } from '@/components/error-message';
import { toast } from '@/lib/toast';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { useRegisterDriver } from '../api/mutations';

// Phone number validation schema
const phoneRegistrationSchema = z.object({
   phoneNumber: z
      .string()
      .min(1, 'Phone number is required')
      .regex(
         /^\+[1-9]\d{1,14}$/,
         'Please enter a valid international phone number (e.g., +1234567890)'
      ),
});

type PhoneRegistrationFormValues = z.infer<typeof phoneRegistrationSchema>;

interface DriverRegistrationProps {
   onSuccess: (phoneNumber: string) => void;
   onCancel: () => void;
}

export const DriverRegistration = ({ onSuccess, onCancel }: DriverRegistrationProps) => {
   const registerDriverMutation = useRegisterDriver();

   const form = useForm<PhoneRegistrationFormValues>({
      resolver: zodResolver(phoneRegistrationSchema),
      defaultValues: {
         phoneNumber: '',
      },
   });

   const {
      control,
      handleSubmit,
      formState: { errors },
   } = form;

   const onSubmit = async (data: PhoneRegistrationFormValues) => {
      registerDriverMutation.mutate(
         {
            phoneNumber: data.phoneNumber,
         },
         {
            onSuccess: () => {
               toast.success('OTP sent successfully to your phone number');
               onSuccess(data.phoneNumber);
            },
            onError: (error: any) => {
               toast.error(error?.message || 'Failed to send OTP. Please try again.');
            },
         }
      );
   };

   return (
      <div className='space-y-6'>
         <div className='text-center space-y-2'>
            <h3 className='text-lg font-semibold'>Register New Driver</h3>
            <p className='text-sm text-muted-foreground'>
               Enter the driver's phone number to send an OTP for verification
            </p>
         </div>

         <Form {...form}>
            <form onSubmit={handleSubmit(onSubmit)} className='space-y-4'>
               <FormField
                  control={control}
                  name='phoneNumber'
                  render={({ field }) => (
                     <FormItem>
                        <FormLabel>Phone Number *</FormLabel>
                        <FormControl>
                           <Input
                              {...field}
                              placeholder='+1234567890'
                              disabled={registerDriverMutation.isPending}
                              className='w-full'
                           />
                        </FormControl>
                        <ErrorMessage error={errors.phoneNumber} />
                        <p className='text-xs text-muted-foreground'>
                           Please include country code (e.g., +91 for India, +1 for US)
                        </p>
                     </FormItem>
                  )}
               />

               <div className='flex gap-3 pt-4'>
                  <Button
                     type='button'
                     variant='outline'
                     onClick={onCancel}
                     disabled={registerDriverMutation.isPending}
                     className='flex-1'
                  >
                     Cancel
                  </Button>
                  <Button
                     type='submit'
                     disabled={registerDriverMutation.isPending}
                     className='flex-1'
                  >
                     {registerDriverMutation.isPending ? (
                        <>
                           Sending OTP...
                           <Spinner className='ml-2 h-4 w-4' />
                        </>
                     ) : (
                        'Send OTP'
                     )}
                  </Button>
               </div>
            </form>
         </Form>
      </div>
   );
};
